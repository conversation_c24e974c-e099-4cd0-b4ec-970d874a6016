"""
融合引擎主类
Fusion Engine Main Class
"""

import cv2
import numpy as np
from typing import List, Tuple, Optional, Dict, Any, Union
from enum import Enum
from pathlib import Path
from dataclasses import dataclass, field

from ..utils.logger import Logger
from ..utils.config_manager import ConfigManager
from ..utils.performance_monitor import PerformanceMonitor, PerformanceOptimizer
from ..utils.thread_pool import VideoProcessingPool
from ..utils.memory_manager import MemoryManager
from ..video.video_loader import VideoLoader
from .insertion_fusion import InsertionFusion, InsertionPosition, InsertionMode
from .overlay_fusion import OverlayFusion, OverlayPosition, OverlayMode
from .blend_fusion import BlendFusion, BlendRegion, BlendMode


class FusionType(Enum):
    """融合类型枚举"""
    INSERTION = "insertion"  # 插入融合
    OVERLAY = "overlay"      # 叠加融合
    BLEND = "blend"          # 混合融合


class TimeDistributionMode(Enum):
    """时间分布模式枚举"""
    UNIFORM = "uniform"  # 均匀分布
    RANDOM = "random"    # 随机分布
    FRONT_BIASED = "front_biased"    # 偏向前段
    MIDDLE_BIASED = "middle_biased"  # 偏向中段
    REAR_BIASED = "rear_biased"      # 偏向后段
    CUSTOM = "custom"    # 自定义时间点


class ScaleMode(Enum):
    """缩放模式枚举"""
    PROPORTIONAL = "proportional"  # 等比例缩放
    STRETCH = "stretch"            # 拉伸缩放
    CROP = "crop"                  # 裁剪缩放
    PAD = "pad"                    # 填充缩放


class MotionTrajectory(Enum):
    """运动轨迹枚举"""
    STATIC = "static"              # 静态位置
    LINEAR_RANDOM = "linear_random"    # 随机直线
    CURVE_RANDOM = "curve_random"      # 随机曲线
    CIRCULAR = "circular"              # 圆形轨迹
    ELLIPTICAL = "elliptical"          # 椭圆轨迹
    HORIZONTAL_SCROLL = "horizontal_scroll"  # 水平滚动
    VERTICAL_SCROLL = "vertical_scroll"      # 垂直滚动
    DIAGONAL = "diagonal"              # 对角线运动
    CUSTOM_PATH = "custom_path"        # 自定义路径


@dataclass
class TimeDimensionControl:
    """时间维度控制参数"""
    # 融合频次控制
    insertion_count: int = 3                    # B视频帧插入A视频的总次数
    distribution_mode: TimeDistributionMode = TimeDistributionMode.UNIFORM  # 时间分布模式

    # 时间位置控制
    custom_time_points: List[float] = field(default_factory=list)  # 自定义时间点（百分比）
    time_bias_strength: float = 0.7            # 偏向强度（0.5-1.0）

    def to_dict(self) -> Dict[str, Any]:
        return {
            'insertion_count': self.insertion_count,
            'distribution_mode': self.distribution_mode.value,
            'custom_time_points': self.custom_time_points,
            'time_bias_strength': self.time_bias_strength
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TimeDimensionControl':
        return cls(
            insertion_count=data.get('insertion_count', 3),
            distribution_mode=TimeDistributionMode(data.get('distribution_mode', 'uniform')),
            custom_time_points=data.get('custom_time_points', []),
            time_bias_strength=data.get('time_bias_strength', 0.7)
        )


@dataclass
class SpatialSizeControl:
    """空间尺寸控制参数"""
    # 图像尺寸对齐
    align_to_main_video: bool = True           # 是否对齐A视频图像尺寸
    scale_ratio: float = 1.0                   # B视频缩放比例（相对于A视频尺寸）
    maintain_aspect_ratio: bool = True         # 是否保持长宽比

    # 缩放模式
    scale_mode: ScaleMode = ScaleMode.PROPORTIONAL  # 缩放模式

    def to_dict(self) -> Dict[str, Any]:
        return {
            'align_to_main_video': self.align_to_main_video,
            'scale_ratio': self.scale_ratio,
            'maintain_aspect_ratio': self.maintain_aspect_ratio,
            'scale_mode': self.scale_mode.value
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SpatialSizeControl':
        return cls(
            align_to_main_video=data.get('align_to_main_video', True),
            scale_ratio=data.get('scale_ratio', 1.0),
            maintain_aspect_ratio=data.get('maintain_aspect_ratio', True),
            scale_mode=ScaleMode(data.get('scale_mode', 'proportional'))
        )


@dataclass
class SpatialPositionControl:
    """空间位置控制参数"""
    # 静态位置模式
    is_static: bool = True                     # 是否为静态位置
    static_position_x: float = 0.5             # 静态位置X坐标（百分比）
    static_position_y: float = 0.5             # 静态位置Y坐标（百分比）

    # 动态位置模式
    motion_trajectory: MotionTrajectory = MotionTrajectory.STATIC  # 运动轨迹
    motion_speed: float = 1.0                  # 移动速度（像素/帧）
    motion_range_limit: Tuple[float, float, float, float] = (0.0, 0.0, 1.0, 1.0)  # 运动范围限制(x1,y1,x2,y2)

    # 自定义路径（用于CUSTOM_PATH模式）
    custom_path_points: List[Tuple[float, float]] = field(default_factory=list)  # 自定义路径点

    def to_dict(self) -> Dict[str, Any]:
        return {
            'is_static': self.is_static,
            'static_position_x': self.static_position_x,
            'static_position_y': self.static_position_y,
            'motion_trajectory': self.motion_trajectory.value,
            'motion_speed': self.motion_speed,
            'motion_range_limit': list(self.motion_range_limit),
            'custom_path_points': self.custom_path_points
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SpatialPositionControl':
        return cls(
            is_static=data.get('is_static', True),
            static_position_x=data.get('static_position_x', 0.5),
            static_position_y=data.get('static_position_y', 0.5),
            motion_trajectory=MotionTrajectory(data.get('motion_trajectory', 'static')),
            motion_speed=data.get('motion_speed', 1.0),
            motion_range_limit=tuple(data.get('motion_range_limit', [0.0, 0.0, 1.0, 1.0])),
            custom_path_points=data.get('custom_path_points', [])
        )


@dataclass
class ImageProcessingControl:
    """图像内容处理控制参数"""
    # 预处理选项
    enable_preprocessing: bool = False         # 是否启用预处理
    preprocessing_methods: List[str] = field(default_factory=list)  # 预处理方法列表

    # 边缘检测
    edge_detection_method: str = "canny"       # 边缘检测方法
    edge_low_threshold: int = 50               # 边缘检测低阈值
    edge_high_threshold: int = 150             # 边缘检测高阈值

    # 直方图处理
    histogram_equalization: bool = False       # 直方图均衡化
    histogram_matching: bool = False           # 直方图匹配
    gamma_correction: float = 1.0              # Gamma校正值

    # 融合方式
    fusion_method: str = "alpha_blend"         # 融合方法
    blend_weight: float = 0.5                  # 混合权重
    feather_radius: int = 5                    # 羽化半径

    def to_dict(self) -> Dict[str, Any]:
        return {
            'enable_preprocessing': self.enable_preprocessing,
            'preprocessing_methods': self.preprocessing_methods,
            'edge_detection_method': self.edge_detection_method,
            'edge_low_threshold': self.edge_low_threshold,
            'edge_high_threshold': self.edge_high_threshold,
            'histogram_equalization': self.histogram_equalization,
            'histogram_matching': self.histogram_matching,
            'gamma_correction': self.gamma_correction,
            'fusion_method': self.fusion_method,
            'blend_weight': self.blend_weight,
            'feather_radius': self.feather_radius
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ImageProcessingControl':
        return cls(
            enable_preprocessing=data.get('enable_preprocessing', False),
            preprocessing_methods=data.get('preprocessing_methods', []),
            edge_detection_method=data.get('edge_detection_method', 'canny'),
            edge_low_threshold=data.get('edge_low_threshold', 50),
            edge_high_threshold=data.get('edge_high_threshold', 150),
            histogram_equalization=data.get('histogram_equalization', False),
            histogram_matching=data.get('histogram_matching', False),
            gamma_correction=data.get('gamma_correction', 1.0),
            fusion_method=data.get('fusion_method', 'alpha_blend'),
            blend_weight=data.get('blend_weight', 0.5),
            feather_radius=data.get('feather_radius', 5)
        )


@dataclass
class TextContentControl:
    """文字内容控制参数"""
    # 文字内容设置
    enable_text_overlay: bool = False          # 是否启用文字叠加
    text_content: str = ""                     # 文字内容
    text_encoding: str = "utf-8"               # 文字编码格式

    # 文字位置控制
    text_position_mode: str = "static"         # 位置模式（static/dynamic）
    static_position_x: float = 0.5             # 静态位置X坐标（百分比）
    static_position_y: float = 0.5             # 静态位置Y坐标（百分比）

    # 动态位置控制
    text_motion_trajectory: MotionTrajectory = MotionTrajectory.STATIC  # 文字运动轨迹
    text_motion_speed: float = 1.0             # 文字移动速度

    # 文字样式控制
    font_family: str = "Arial"                 # 字体类型
    font_size: int = 24                        # 字体大小
    font_color: Tuple[int, int, int] = (255, 255, 255)  # 字体颜色(RGB)
    font_alpha: float = 1.0                    # 字体透明度
    font_bold: bool = False                    # 是否粗体
    font_italic: bool = False                  # 是否斜体

    # 文字效果
    enable_outline: bool = False               # 是否启用描边
    outline_color: Tuple[int, int, int] = (0, 0, 0)  # 描边颜色
    outline_width: int = 1                     # 描边宽度
    enable_shadow: bool = False                # 是否启用阴影
    shadow_offset: Tuple[int, int] = (2, 2)    # 阴影偏移
    shadow_color: Tuple[int, int, int] = (0, 0, 0)  # 阴影颜色

    # 时间控制
    appearance_frequency: int = 1              # 文字出现频次
    continuous_frames: int = 30                # 连续显示帧数
    enable_fade_effect: bool = False           # 是否启用淡入淡出效果
    fade_duration: int = 10                    # 淡入淡出持续帧数

    def to_dict(self) -> Dict[str, Any]:
        return {
            'enable_text_overlay': self.enable_text_overlay,
            'text_content': self.text_content,
            'text_encoding': self.text_encoding,
            'text_position_mode': self.text_position_mode,
            'static_position_x': self.static_position_x,
            'static_position_y': self.static_position_y,
            'text_motion_trajectory': self.text_motion_trajectory.value,
            'text_motion_speed': self.text_motion_speed,
            'font_family': self.font_family,
            'font_size': self.font_size,
            'font_color': list(self.font_color),
            'font_alpha': self.font_alpha,
            'font_bold': self.font_bold,
            'font_italic': self.font_italic,
            'enable_outline': self.enable_outline,
            'outline_color': list(self.outline_color),
            'outline_width': self.outline_width,
            'enable_shadow': self.enable_shadow,
            'shadow_offset': list(self.shadow_offset),
            'shadow_color': list(self.shadow_color),
            'appearance_frequency': self.appearance_frequency,
            'continuous_frames': self.continuous_frames,
            'enable_fade_effect': self.enable_fade_effect,
            'fade_duration': self.fade_duration
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TextContentControl':
        return cls(
            enable_text_overlay=data.get('enable_text_overlay', False),
            text_content=data.get('text_content', ''),
            text_encoding=data.get('text_encoding', 'utf-8'),
            text_position_mode=data.get('text_position_mode', 'static'),
            static_position_x=data.get('static_position_x', 0.5),
            static_position_y=data.get('static_position_y', 0.5),
            text_motion_trajectory=MotionTrajectory(data.get('text_motion_trajectory', 'static')),
            text_motion_speed=data.get('text_motion_speed', 1.0),
            font_family=data.get('font_family', 'Arial'),
            font_size=data.get('font_size', 24),
            font_color=tuple(data.get('font_color', [255, 255, 255])),
            font_alpha=data.get('font_alpha', 1.0),
            font_bold=data.get('font_bold', False),
            font_italic=data.get('font_italic', False),
            enable_outline=data.get('enable_outline', False),
            outline_color=tuple(data.get('outline_color', [0, 0, 0])),
            outline_width=data.get('outline_width', 1),
            enable_shadow=data.get('enable_shadow', False),
            shadow_offset=tuple(data.get('shadow_offset', [2, 2])),
            shadow_color=tuple(data.get('shadow_color', [0, 0, 0])),
            appearance_frequency=data.get('appearance_frequency', 1),
            continuous_frames=data.get('continuous_frames', 30),
            enable_fade_effect=data.get('enable_fade_effect', False),
            fade_duration=data.get('fade_duration', 10)
        )


class FusionParams:
    """融合参数类"""
    
    def __init__(self):
        # 原有参数（保持向后兼容）
        self.fusion_type: FusionType = FusionType.INSERTION
        self.insertion_mode: InsertionMode = InsertionMode.DIRECT
        self.resize_mode: str = "fit"
        self.alpha: float = 0.5
        self.positions: List[InsertionPosition] = []
        self.output_fps: float = 30.0
        self.output_codec: str = 'mp4v'

        # 新的五个控制维度参数
        self.time_dimension: TimeDimensionControl = TimeDimensionControl()
        self.spatial_size: SpatialSizeControl = SpatialSizeControl()
        self.spatial_position: SpatialPositionControl = SpatialPositionControl()
        self.image_processing: ImageProcessingControl = ImageProcessingControl()
        self.text_content: TextContentControl = TextContentControl()
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            # 原有参数
            'fusion_type': self.fusion_type.value,
            'insertion_mode': self.insertion_mode.value,
            'resize_mode': self.resize_mode,
            'alpha': self.alpha,
            'positions': [{'frame': pos.frame_number, 'duration': pos.duration}
                         for pos in self.positions],
            'output_fps': self.output_fps,
            'output_codec': self.output_codec,

            # 新的五个控制维度参数
            'time_dimension': self.time_dimension.to_dict(),
            'spatial_size': self.spatial_size.to_dict(),
            'spatial_position': self.spatial_position.to_dict(),
            'image_processing': self.image_processing.to_dict(),
            'text_content': self.text_content.to_dict()
        }
    
    def from_dict(self, data: Dict[str, Any]):
        """从字典加载"""
        # 原有参数
        self.fusion_type = FusionType(data.get('fusion_type', 'insertion'))
        self.insertion_mode = InsertionMode(data.get('insertion_mode', 'direct'))
        self.resize_mode = data.get('resize_mode', 'fit')
        self.alpha = data.get('alpha', 0.5)
        self.output_fps = data.get('output_fps', 30.0)
        self.output_codec = data.get('output_codec', 'mp4v')

        # 加载位置信息
        positions_data = data.get('positions', [])
        self.positions = [InsertionPosition(pos['frame'], pos['duration'])
                         for pos in positions_data]

        # 加载新的五个控制维度参数
        if 'time_dimension' in data:
            self.time_dimension = TimeDimensionControl.from_dict(data['time_dimension'])
        else:
            self.time_dimension = TimeDimensionControl()

        if 'spatial_size' in data:
            self.spatial_size = SpatialSizeControl.from_dict(data['spatial_size'])
        else:
            self.spatial_size = SpatialSizeControl()

        if 'spatial_position' in data:
            self.spatial_position = SpatialPositionControl.from_dict(data['spatial_position'])
        else:
            self.spatial_position = SpatialPositionControl()

        if 'image_processing' in data:
            self.image_processing = ImageProcessingControl.from_dict(data['image_processing'])
        else:
            self.image_processing = ImageProcessingControl()

        if 'text_content' in data:
            self.text_content = TextContentControl.from_dict(data['text_content'])
        else:
            self.text_content = TextContentControl()


class FusionEngine:
    """融合引擎主类"""
    
    def __init__(self):
        self.logger = Logger.get_logger(__name__)
        self.config = ConfigManager()
        
        # 视频加载器
        self.video_a_loader: Optional[VideoLoader] = None
        self.video_b_loader: Optional[VideoLoader] = None
        
        # 融合器
        self.insertion_fusion = InsertionFusion()
        self.overlay_fusion = OverlayFusion()
        self.blend_fusion = BlendFusion()

        # 性能优化组件
        self.performance_monitor = PerformanceMonitor()
        self.performance_optimizer = PerformanceOptimizer(self.performance_monitor)
        self.memory_manager = MemoryManager()
        self.thread_pool: Optional[VideoProcessingPool] = None

        # 性能设置
        self.enable_performance_monitoring = True
        self.enable_multithreading = True
        self.enable_memory_optimization = True
        
        # 融合参数
        self.fusion_params = FusionParams()
        
        # 结果
        self.result_frames: List[np.ndarray] = []
        
        self.logger.info("融合引擎初始化完成")
    
    def load_video_a(self, file_path: str) -> bool:
        """加载A视频"""
        try:
            self.video_a_loader = VideoLoader()
            video_info = self.video_a_loader.load_video(file_path)
            
            if video_info is None:
                self.logger.error(f"加载A视频失败: {file_path}")
                return False
            
            self.logger.info(f"A视频加载成功: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载A视频时发生错误: {e}")
            return False
    
    def load_video_b(self, file_path: str) -> bool:
        """加载B视频"""
        try:
            self.video_b_loader = VideoLoader()
            video_info = self.video_b_loader.load_video(file_path)
            
            if video_info is None:
                self.logger.error(f"加载B视频失败: {file_path}")
                return False
            
            self.logger.info(f"B视频加载成功: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载B视频时发生错误: {e}")
            return False
    
    def set_fusion_params(self, params: FusionParams):
        """设置融合参数"""
        # 保留已有的插入位置
        existing_positions = self.fusion_params.positions.copy()
        self.fusion_params = params
        if not self.fusion_params.positions and existing_positions:
            self.fusion_params.positions = existing_positions
        self.logger.info(f"融合参数已设置: {params.to_dict()}")
    
    def add_insertion_position(self, frame_number: int, duration: int = 1):
        """添加插入位置"""
        position = InsertionPosition(frame_number, duration)
        self.fusion_params.positions.append(position)
        self.logger.info(f"添加插入位置: {position}")
    
    def clear_insertion_positions(self):
        """清除所有插入位置"""
        self.fusion_params.positions.clear()
        self.logger.info("已清除所有插入位置")
    
    def get_video_info(self) -> Dict[str, Any]:
        """获取视频信息"""
        info = {
            'video_a': None,
            'video_b': None,
            'compatible': False
        }
        
        if self.video_a_loader and self.video_a_loader.is_loaded():
            info['video_a'] = self.video_a_loader.get_current_info().to_dict()
        
        if self.video_b_loader and self.video_b_loader.is_loaded():
            info['video_b'] = self.video_b_loader.get_current_info().to_dict()
        
        # 检查兼容性
        if info['video_a'] and info['video_b']:
            info['compatible'] = True  # 基本兼容性检查
        
        return info
    
    def validate_fusion_params(self) -> Tuple[bool, str]:
        """验证融合参数"""
        try:
            # 检查视频是否加载
            if not self.video_a_loader or not self.video_a_loader.is_loaded():
                return False, "A视频未加载"
            
            if not self.video_b_loader or not self.video_b_loader.is_loaded():
                return False, "B视频未加载"
            
            # 检查插入位置
            if self.fusion_params.fusion_type == FusionType.INSERTION:
                if not self.fusion_params.positions:
                    return False, "未设置插入位置"
                
                video_b_info = self.video_b_loader.get_current_info()
                for pos in self.fusion_params.positions:
                    if pos.frame_number < 0 or pos.frame_number >= video_b_info.frame_count:
                        return False, f"插入位置 {pos.frame_number} 超出B视频范围"
                    
                    if pos.duration <= 0:
                        return False, f"插入持续时间必须大于0"
            
            # 检查其他参数
            if self.fusion_params.alpha < 0 or self.fusion_params.alpha > 1:
                return False, "透明度值必须在0-1之间"
            
            if self.fusion_params.output_fps <= 0:
                return False, "输出帧率必须大于0"
            
            return True, "参数验证通过"
            
        except Exception as e:
            return False, f"参数验证失败: {e}"
    
    def execute_fusion(self) -> bool:
        """执行融合"""
        try:
            # 验证参数
            is_valid, message = self.validate_fusion_params()
            if not is_valid:
                self.logger.error(f"融合参数验证失败: {message}")
                return False

            # 性能优化准备
            self._prepare_performance_optimization()

            self.logger.info("开始执行融合")

            # 开始性能监控
            if self.enable_performance_monitoring:
                self.performance_monitor.start_monitoring()
                self.performance_monitor.start_processing()

            try:
                # 根据融合类型执行相应的融合
                if self.fusion_params.fusion_type == FusionType.INSERTION:
                    success = self._execute_insertion_fusion()
                elif self.fusion_params.fusion_type == FusionType.OVERLAY:
                    success = self._execute_overlay_fusion()
                elif self.fusion_params.fusion_type == FusionType.BLEND:
                    success = self._execute_blend_fusion()
                else:
                    self.logger.error(f"不支持的融合类型: {self.fusion_params.fusion_type}")
                    return False

                if success:
                    self.logger.info("融合执行完成")

                    # 输出性能统计
                    if self.enable_performance_monitoring:
                        stats = self.performance_monitor.stop_processing()
                        self.logger.info(f"性能统计: {stats}")

                    return True
                else:
                    self.logger.error("融合执行失败")
                    return False

            finally:
                # 停止性能监控
                if self.enable_performance_monitoring:
                    self.performance_monitor.stop_monitoring()

                # 清理资源
                self._cleanup_resources()

        except Exception as e:
            self.logger.error(f"执行融合时发生错误: {e}")
            return False
    
    def _execute_insertion_fusion(self) -> bool:
        """执行插入融合"""
        try:
            # 设置视频
            self.insertion_fusion.set_videos(self.video_a_loader, self.video_b_loader)
            
            # 根据插入模式执行融合
            if self.fusion_params.insertion_mode == InsertionMode.DIRECT:
                self.result_frames = self.insertion_fusion.direct_insertion(
                    self.fusion_params.positions, 
                    self.fusion_params.resize_mode
                )
            elif self.fusion_params.insertion_mode == InsertionMode.REPLACE:
                self.result_frames = self.insertion_fusion.replace_insertion(
                    self.fusion_params.positions, 
                    self.fusion_params.resize_mode
                )
            else:
                self.logger.error(f"不支持的插入模式: {self.fusion_params.insertion_mode}")
                return False
            
            return len(self.result_frames) > 0
            
        except Exception as e:
            self.logger.error(f"执行插入融合失败: {e}")
            return False

    def _execute_overlay_fusion(self) -> bool:
        """执行叠加融合"""
        try:
            # 设置视频
            self.overlay_fusion.set_videos(self.video_a_loader, self.video_b_loader)

            # 创建叠加位置（这里使用默认位置，实际应用中可以从参数中获取）
            from .overlay_fusion import OverlayPosition
            position = OverlayPosition(x=50, y=50, width=200, height=150)

            # 执行叠加融合
            self.result_frames = self.overlay_fusion.overlay_fusion(
                position=position,
                alpha=self.fusion_params.alpha,
                mode=OverlayMode.NORMAL,
                resize_mode=self.fusion_params.resize_mode
            )

            return len(self.result_frames) > 0

        except Exception as e:
            self.logger.error(f"执行叠加融合失败: {e}")
            return False

    def _execute_blend_fusion(self) -> bool:
        """执行混合融合"""
        try:
            # 设置视频
            self.blend_fusion.set_videos(self.video_a_loader, self.video_b_loader)

            # 执行混合融合
            self.result_frames = self.blend_fusion.blend_fusion(
                alpha=self.fusion_params.alpha,
                mode=BlendMode.LINEAR,
                resize_mode=self.fusion_params.resize_mode
            )

            return len(self.result_frames) > 0

        except Exception as e:
            self.logger.error(f"执行混合融合失败: {e}")
            return False
    
    def get_fusion_preview(self, max_frames: int = 5) -> List[Tuple[int, np.ndarray]]:
        """获取融合预览"""
        try:
            if self.fusion_params.fusion_type == FusionType.INSERTION:
                # 确保视频已设置
                if self.video_a_loader and self.video_b_loader:
                    self.insertion_fusion.set_videos(self.video_a_loader, self.video_b_loader)

                return self.insertion_fusion.get_insertion_preview(
                    self.fusion_params.positions, max_frames
                )

            elif self.fusion_params.fusion_type == FusionType.BLEND:
                # 混合融合预览
                if self.video_a_loader and self.video_b_loader:
                    self.blend_fusion.set_videos(self.video_a_loader, self.video_b_loader)

                return self.blend_fusion.get_blend_preview(max_frames)

            elif self.fusion_params.fusion_type == FusionType.OVERLAY:
                # 叠加融合预览
                if self.video_a_loader and self.video_b_loader:
                    self.overlay_fusion.set_videos(self.video_a_loader, self.video_b_loader)

                return self.overlay_fusion.get_overlay_preview(max_frames)

            else:
                self.logger.warning(f"未知的融合类型: {self.fusion_params.fusion_type}")
                return []

        except Exception as e:
            self.logger.error(f"获取融合预览失败: {e}")
            return []
    
    def save_result(self, output_path: str) -> bool:
        """保存融合结果"""
        try:
            if not self.result_frames:
                self.logger.error("没有可保存的融合结果")
                return False
            
            # 确保输出目录存在
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 保存视频
            if self.fusion_params.fusion_type == FusionType.INSERTION:
                return self.insertion_fusion.save_result_video(
                    output_path,
                    self.fusion_params.output_fps,
                    self.fusion_params.output_codec
                )
            elif self.fusion_params.fusion_type == FusionType.OVERLAY:
                return self.overlay_fusion.save_result_video(
                    output_path,
                    self.fusion_params.output_fps,
                    self.fusion_params.output_codec
                )
            elif self.fusion_params.fusion_type == FusionType.BLEND:
                return self.blend_fusion.save_result_video(
                    output_path,
                    self.fusion_params.output_fps,
                    self.fusion_params.output_codec
                )
            else:
                self.logger.error(f"不支持的融合类型保存: {self.fusion_params.fusion_type}")
                return False
                
        except Exception as e:
            self.logger.error(f"保存融合结果失败: {e}")
            return False
    
    def get_result_frames(self) -> List[np.ndarray]:
        """获取结果帧"""
        return self.result_frames.copy()
    
    def get_result_info(self) -> Dict[str, Any]:
        """获取结果信息"""
        if not self.result_frames:
            return {}
        
        first_frame = self.result_frames[0]
        h, w = first_frame.shape[:2]
        
        return {
            'frame_count': len(self.result_frames),
            'width': w,
            'height': h,
            'duration': len(self.result_frames) / self.fusion_params.output_fps,
            'fps': self.fusion_params.output_fps
        }
    
    def save_fusion_params(self, file_path: str) -> bool:
        """保存融合参数到文件"""
        try:
            import json
            
            params_dict = self.fusion_params.to_dict()
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(params_dict, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"融合参数已保存到: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存融合参数失败: {e}")
            return False
    
    def load_fusion_params(self, file_path: str) -> bool:
        """从文件加载融合参数"""
        try:
            import json
            
            with open(file_path, 'r', encoding='utf-8') as f:
                params_dict = json.load(f)
            
            self.fusion_params.from_dict(params_dict)
            
            self.logger.info(f"融合参数已从文件加载: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载融合参数失败: {e}")
            return False
    
    def _prepare_performance_optimization(self):
        """准备性能优化"""
        try:
            # 获取视频信息
            video_a_info = self.video_a_loader.get_current_info()
            video_b_info = self.video_b_loader.get_current_info()

            # 估算内存需求
            video_info = {
                "width": max(video_a_info.width, video_b_info.width),
                "height": max(video_a_info.height, video_b_info.height),
                "frame_count": max(video_a_info.frame_count, video_b_info.frame_count),
                "file_size_mb": (video_a_info.file_size + video_b_info.file_size) / (1024 * 1024)
            }

            # 内存优化
            if self.enable_memory_optimization:
                self.memory_manager.optimize_for_large_video(video_info["file_size_mb"])

            # 线程池优化
            if self.enable_multithreading:
                optimal_params = self.memory_manager.get_optimal_processing_params(video_info)

                if optimal_params["use_threading"]:
                    self.thread_pool = VideoProcessingPool(
                        max_workers=optimal_params["max_threads"],
                        monitor=self.performance_monitor
                    )
                    self.thread_pool.start()
                    self.thread_pool.set_chunk_size(optimal_params["chunk_size"])

            # 获取优化建议
            suggestions = self.performance_optimizer.suggest_optimizations(video_info)
            for suggestion in suggestions:
                self.logger.info(f"优化建议: {suggestion}")

        except Exception as e:
            self.logger.error(f"性能优化准备失败: {e}")

    def _cleanup_resources(self):
        """清理资源"""
        try:
            # 关闭线程池
            if self.thread_pool:
                self.thread_pool.shutdown()
                self.thread_pool = None

            # 清理内存
            if self.enable_memory_optimization:
                self.memory_manager.cleanup_memory()

        except Exception as e:
            self.logger.error(f"资源清理失败: {e}")

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        try:
            stats = {
                "performance_monitor": {},
                "memory_manager": {},
                "thread_pool": {}
            }

            # 性能监控统计
            if self.performance_monitor:
                current_metrics = self.performance_monitor.get_current_metrics()
                average_metrics = self.performance_monitor.get_average_metrics()
                peak_metrics = self.performance_monitor.get_peak_metrics()

                stats["performance_monitor"] = {
                    "current": current_metrics.__dict__ if current_metrics else {},
                    "average": average_metrics,
                    "peak": peak_metrics
                }

            # 内存管理统计
            if self.memory_manager:
                stats["memory_manager"] = self.memory_manager.get_statistics()

            # 线程池统计
            if self.thread_pool:
                stats["thread_pool"] = self.thread_pool.thread_pool.get_statistics()

            return stats

        except Exception as e:
            self.logger.error(f"获取性能统计失败: {e}")
            return {}

    def set_performance_settings(self, enable_monitoring: bool = True,
                                enable_multithreading: bool = True,
                                enable_memory_optimization: bool = True):
        """设置性能优化选项"""
        self.enable_performance_monitoring = enable_monitoring
        self.enable_multithreading = enable_multithreading
        self.enable_memory_optimization = enable_memory_optimization

        self.logger.info(f"性能设置更新 - 监控: {enable_monitoring}, "
                        f"多线程: {enable_multithreading}, 内存优化: {enable_memory_optimization}")

    def export_performance_report(self, output_path: str) -> bool:
        """导出性能报告"""
        try:
            stats = self.get_performance_stats()

            import json
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(stats, f, indent=2, ensure_ascii=False, default=str)

            self.logger.info(f"性能报告已导出: {output_path}")
            return True

        except Exception as e:
            self.logger.error(f"导出性能报告失败: {e}")
            return False

    def reset(self):
        """重置融合引擎"""
        # 清理资源
        self._cleanup_resources()

        # 重置状态
        self.video_a_loader = None
        self.video_b_loader = None
        self.result_frames.clear()
        self.fusion_params = FusionParams()

        # 重置性能组件
        if self.performance_monitor:
            self.performance_monitor.clear_history()

        self.logger.info("融合引擎已重置")
