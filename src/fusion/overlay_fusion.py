"""
叠加融合模块
Overlay Fusion Module
"""

import cv2
import numpy as np
from typing import List, Tuple, Optional, Dict, Any, Union
from enum import Enum

from ..utils.logger import Logger
from ..video.video_loader import VideoLoader


class OverlayMode(Enum):
    """叠加模式枚举"""
    NORMAL = "normal"          # 正常叠加
    MULTIPLY = "multiply"      # 正片叠底
    SCREEN = "screen"          # 滤色
    OVERLAY = "overlay"        # 叠加
    SOFT_LIGHT = "soft_light"  # 柔光
    HARD_LIGHT = "hard_light"  # 强光


class OverlayPosition:
    """叠加位置类"""

    def __init__(self, x: int = 0, y: int = 0, width: Optional[int] = None, height: Optional[int] = None):
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.is_moving = False
        self.start_pos: Optional[Tuple[int, int]] = None
        self.end_pos: Optional[Tuple[int, int]] = None

    def set_moving(self, start_pos: Tuple[int, int], end_pos: Tuple[int, int]):
        """设置移动轨迹"""
        self.is_moving = True
        self.start_pos = start_pos
        self.end_pos = end_pos

    def get_position_at_frame(self, frame_index: int, total_frames: int) -> Tuple[int, int]:
        """获取指定帧的位置"""
        if not self.is_moving or not self.start_pos or not self.end_pos:
            return (self.x, self.y)

        if total_frames <= 1:
            return self.start_pos

        progress = frame_index / (total_frames - 1)
        progress = max(0, min(1, progress))  # 限制在0-1范围内

        x = int(self.start_pos[0] + (self.end_pos[0] - self.start_pos[0]) * progress)
        y = int(self.start_pos[1] + (self.end_pos[1] - self.start_pos[1]) * progress)

        return (x, y)


class OverlayFusion:
    """叠加融合类"""

    def __init__(self):
        self.logger = Logger.get_logger(__name__)
        self.video_a_loader: Optional[VideoLoader] = None
        self.video_b_loader: Optional[VideoLoader] = None
        self.result_frames: List[np.ndarray] = []
        self.logger.info("叠加融合模块初始化完成")

    def set_videos(self, video_a_loader: VideoLoader, video_b_loader: VideoLoader):
        """设置要融合的视频"""
        if not video_a_loader.is_loaded():
            raise ValueError("视频A未正确加载")
        if not video_b_loader.is_loaded():
            raise ValueError("视频B未正确加载")

        self.video_a_loader = video_a_loader
        self.video_b_loader = video_b_loader

        self.logger.info("叠加融合视频设置完成")

    def overlay_fusion(self, position: OverlayPosition,
                      alpha: float = 0.5,
                      mode: OverlayMode = OverlayMode.NORMAL,
                      resize_mode: str = "fit",
                      frame_range: Optional[Tuple[int, int]] = None) -> List[np.ndarray]:
        """执行叠加融合

        Args:
            position: 叠加位置
            alpha: 透明度 (0.0-1.0)
            mode: 叠加模式
            resize_mode: A视频调整模式
            frame_range: 融合帧范围 (start_frame, end_frame)

        Returns:
            融合后的帧序列
        """
        try:
            if not self.video_a_loader or not self.video_b_loader:
                raise ValueError("视频未设置")

            video_a_info = self.video_a_loader.get_current_info()
            video_b_info = self.video_b_loader.get_current_info()

            self.logger.info(f"开始叠加融合，模式: {mode.value}, 透明度: {alpha}")

            # 获取B视频的所有帧
            b_frames = []
            for frame_number, frame in self.video_b_loader.get_frame_iterator():
                b_frames.append(frame.copy())

            # 获取A视频的帧
            a_frames = []
            for frame_number, frame in self.video_a_loader.get_frame_iterator():
                a_frames.append(frame.copy())

            if not a_frames or not b_frames:
                raise ValueError("无法获取视频帧")

            # 确定融合范围
            if frame_range:
                start_frame, end_frame = frame_range
                start_frame = max(0, start_frame)
                end_frame = min(len(b_frames), end_frame)
            else:
                start_frame, end_frame = 0, len(b_frames)

            # 计算A视频在融合范围内的帧数
            fusion_frame_count = end_frame - start_frame

            result_frames = []

            for i, b_frame in enumerate(b_frames):
                if start_frame <= i < end_frame:
                    # 在融合范围内，执行叠加
                    fusion_index = i - start_frame
                    a_frame_index = min(fusion_index % len(a_frames), len(a_frames) - 1)
                    a_frame = a_frames[a_frame_index]

                    # 获取当前帧的叠加位置
                    current_pos = position.get_position_at_frame(fusion_index, fusion_frame_count)

                    # 执行叠加
                    overlaid_frame = self._overlay_frame(
                        b_frame, a_frame, current_pos, position, alpha, mode, resize_mode
                    )
                    result_frames.append(overlaid_frame)
                else:
                    # 不在融合范围内，直接使用B帧
                    result_frames.append(b_frame.copy())

            self.result_frames = result_frames
            self.logger.info(f"叠加融合完成，结果帧数: {len(result_frames)}")
            return result_frames

        except Exception as e:
            self.logger.error(f"叠加融合失败: {e}")
            raise

    def _overlay_frame(self, background: np.ndarray,
                      foreground: np.ndarray,
                      position: Tuple[int, int],
                      overlay_pos: OverlayPosition,
                      alpha: float,
                      mode: OverlayMode,
                      resize_mode: str) -> np.ndarray:
        """在背景帧上叠加前景帧"""
        try:
            result = background.copy()
            bg_h, bg_w = background.shape[:2]

            # 确定叠加区域尺寸
            if overlay_pos.width and overlay_pos.height:
                overlay_w, overlay_h = overlay_pos.width, overlay_pos.height
            else:
                overlay_w, overlay_h = foreground.shape[1], foreground.shape[0]

            # 调整前景图像尺寸
            if resize_mode == "fit":
                # 保持宽高比适应
                scale = min(overlay_w / foreground.shape[1], overlay_h / foreground.shape[0])
                new_w = int(foreground.shape[1] * scale)
                new_h = int(foreground.shape[0] * scale)
                resized_fg = cv2.resize(foreground, (new_w, new_h))

                # 居中放置
                x_offset = (overlay_w - new_w) // 2
                y_offset = (overlay_h - new_h) // 2

            elif resize_mode == "stretch":
                # 直接拉伸
                resized_fg = cv2.resize(foreground, (overlay_w, overlay_h))
                x_offset, y_offset = 0, 0
                new_w, new_h = overlay_w, overlay_h

            elif resize_mode == "crop":
                # 保持宽高比裁剪
                scale = max(overlay_w / foreground.shape[1], overlay_h / foreground.shape[0])
                new_w = int(foreground.shape[1] * scale)
                new_h = int(foreground.shape[0] * scale)
                scaled_fg = cv2.resize(foreground, (new_w, new_h))

                # 居中裁剪
                crop_x = (new_w - overlay_w) // 2
                crop_y = (new_h - overlay_h) // 2
                resized_fg = scaled_fg[crop_y:crop_y+overlay_h, crop_x:crop_x+overlay_w]
                x_offset, y_offset = 0, 0
                new_w, new_h = overlay_w, overlay_h

            else:  # "original"
                resized_fg = foreground.copy()
                new_w, new_h = foreground.shape[1], foreground.shape[0]
                x_offset, y_offset = 0, 0

            # 计算在背景图像中的实际位置
            x, y = position
            actual_x = x + x_offset
            actual_y = y + y_offset

            # 确保不超出背景图像边界
            if actual_x >= bg_w or actual_y >= bg_h or actual_x + new_w <= 0 or actual_y + new_h <= 0:
                return result

            # 计算有效的叠加区域
            src_x1 = max(0, -actual_x)
            src_y1 = max(0, -actual_y)
            src_x2 = min(new_w, bg_w - actual_x)
            src_y2 = min(new_h, bg_h - actual_y)

            dst_x1 = max(0, actual_x)
            dst_y1 = max(0, actual_y)
            dst_x2 = dst_x1 + (src_x2 - src_x1)
            dst_y2 = dst_y1 + (src_y2 - src_y1)

            if src_x2 <= src_x1 or src_y2 <= src_y1:
                return result

            # 提取有效区域
            fg_region = resized_fg[src_y1:src_y2, src_x1:src_x2]
            bg_region = result[dst_y1:dst_y2, dst_x1:dst_x2]

            # 应用叠加模式
            blended_region = self._apply_blend_mode(bg_region, fg_region, mode, alpha)

            # 将结果放回背景图像
            result[dst_y1:dst_y2, dst_x1:dst_x2] = blended_region

            return result

        except Exception as e:
            self.logger.error(f"叠加帧失败: {e}")
            return background.copy()

    def _apply_blend_mode(self, background: np.ndarray,
                         foreground: np.ndarray,
                         mode: OverlayMode,
                         alpha: float) -> np.ndarray:
        """应用混合模式"""
        try:
            # 确保数据类型一致
            bg = background.astype(np.float32) / 255.0
            fg = foreground.astype(np.float32) / 255.0

            if mode == OverlayMode.NORMAL:
                # 正常混合
                result = bg * (1 - alpha) + fg * alpha

            elif mode == OverlayMode.MULTIPLY:
                # 正片叠底
                blended = bg * fg
                result = bg * (1 - alpha) + blended * alpha

            elif mode == OverlayMode.SCREEN:
                # 滤色
                blended = 1 - (1 - bg) * (1 - fg)
                result = bg * (1 - alpha) + blended * alpha

            elif mode == OverlayMode.OVERLAY:
                # 叠加
                mask = bg < 0.5
                blended = np.where(mask, 2 * bg * fg, 1 - 2 * (1 - bg) * (1 - fg))
                result = bg * (1 - alpha) + blended * alpha

            elif mode == OverlayMode.SOFT_LIGHT:
                # 柔光
                mask = fg < 0.5
                blended = np.where(mask,
                                 bg - (1 - 2 * fg) * bg * (1 - bg),
                                 bg + (2 * fg - 1) * (np.sqrt(bg) - bg))
                result = bg * (1 - alpha) + blended * alpha

            elif mode == OverlayMode.HARD_LIGHT:
                # 强光
                mask = fg < 0.5
                blended = np.where(mask, 2 * bg * fg, 1 - 2 * (1 - bg) * (1 - fg))
                result = bg * (1 - alpha) + blended * alpha

            else:
                result = bg * (1 - alpha) + fg * alpha

            # 限制到有效范围并转换回uint8
            result = np.clip(result * 255, 0, 255).astype(np.uint8)
            return result

        except Exception as e:
            self.logger.error(f"应用混合模式失败: {e}")
            return background.copy()

    def create_moving_overlay(self, start_pos: Tuple[int, int],
                            end_pos: Tuple[int, int],
                            width: Optional[int] = None,
                            height: Optional[int] = None) -> OverlayPosition:
        """创建移动叠加位置"""
        position = OverlayPosition(width=width, height=height)
        position.set_moving(start_pos, end_pos)
        return position

    def get_result_frames(self) -> List[np.ndarray]:
        """获取融合结果帧"""
        return self.result_frames.copy()

    def save_result_video(self, output_path: str, fps: float = 30.0,
                         codec: str = 'mp4v') -> bool:
        """保存融合结果为视频文件"""
        try:
            if not self.result_frames:
                self.logger.error("没有可保存的结果帧")
                return False

            # 获取第一帧的尺寸
            first_frame = self.result_frames[0]
            h, w = first_frame.shape[:2]

            # 创建视频写入器
            fourcc = cv2.VideoWriter_fourcc(*codec)
            video_writer = cv2.VideoWriter(output_path, fourcc, fps, (w, h))

            if not video_writer.isOpened():
                self.logger.error(f"无法创建视频写入器: {output_path}")
                return False

            # 写入所有帧
            for i, frame in enumerate(self.result_frames):
                video_writer.write(frame)

                if (i + 1) % 100 == 0:
                    self.logger.info(f"已写入 {i + 1}/{len(self.result_frames)} 帧")

            video_writer.release()
            self.logger.info(f"叠加融合视频保存完成: {output_path}")
            return True

        except Exception as e:
            self.logger.error(f"保存叠加融合视频失败: {e}")
            return False

    def get_overlay_preview(self, max_frames: int = 5) -> List[Tuple[int, np.ndarray]]:
        """获取叠加融合预览"""
        try:
            if not self.video_a_loader or not self.video_b_loader:
                self.logger.warning("视频未加载，无法生成预览")
                return []

            preview_frames = []

            # 获取视频信息
            info_a = self.video_a_loader.get_current_info()
            info_b = self.video_b_loader.get_current_info()

            if not info_a or not info_b:
                self.logger.warning("无法获取视频信息")
                return []

            # 计算预览帧的位置（均匀分布）
            min_frames = min(info_a.frame_count, info_b.frame_count)
            if min_frames <= 0:
                return []

            # 生成预览帧索引
            frame_indices = []
            if max_frames >= min_frames:
                frame_indices = list(range(min_frames))
            else:
                step = max(1, min_frames // max_frames)
                frame_indices = [i * step for i in range(max_frames)]
                # 确保包含最后一帧
                if frame_indices[-1] < min_frames - 1:
                    frame_indices[-1] = min_frames - 1

            self.logger.info(f"生成叠加预览，预览帧数: {len(frame_indices)}")

            # 生成预览帧
            for frame_idx in frame_indices:
                # 获取对应帧
                frame_a = self.video_a_loader.get_frame(frame_idx)
                frame_b = self.video_b_loader.get_frame(frame_idx)

                if frame_a is None or frame_b is None:
                    continue

                # 执行简单的叠加融合预览（Alpha混合）
                # 调整帧尺寸使其匹配
                if frame_a.shape != frame_b.shape:
                    frame_b = cv2.resize(frame_b, (frame_a.shape[1], frame_a.shape[0]))

                # 简单的Alpha混合作为预览
                alpha = 0.5  # 默认叠加透明度
                overlaid_frame = cv2.addWeighted(frame_a, 1-alpha, frame_b, alpha, 0)

                if overlaid_frame is not None:
                    preview_frames.append((frame_idx, overlaid_frame))

            self.logger.info(f"叠加预览生成完成，成功生成 {len(preview_frames)} 帧")
            return preview_frames

        except Exception as e:
            self.logger.error(f"生成叠加预览失败: {e}")
            return []
