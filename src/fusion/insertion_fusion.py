"""
插入融合模块
Insertion Fusion Module
"""

import cv2
import numpy as np
import random
import math
from typing import List, Tuple, Optional, Dict, Any
from enum import Enum

from ..utils.logger import Logger
from ..video.video_loader import VideoLoader, VideoInfo


class InsertionMode(Enum):
    """插入模式枚举"""
    DIRECT = "direct"  # 直接插入
    REPLACE = "replace"  # 替换插入
    BLEND = "blend"  # 混合插入


class InsertionPosition:
    """插入位置类"""
    
    def __init__(self, frame_number: int, duration: int = 1):
        self.frame_number = frame_number  # 插入的帧位置
        self.duration = duration  # 插入持续帧数
    
    def __str__(self):
        return f"Position(frame={self.frame_number}, duration={self.duration})"


class InsertionFusion:
    """插入融合类"""
    
    def __init__(self):
        self.logger = Logger.get_logger(__name__)
        self.video_a_loader: Optional[VideoLoader] = None
        self.video_b_loader: Optional[VideoLoader] = None
        self.result_frames: List[np.ndarray] = []
        
    def set_videos(self, video_a_loader: VideoLoader, video_b_loader: VideoLoader):
        """设置要融合的视频"""
        if not video_a_loader.is_loaded():
            raise ValueError("视频A未正确加载")
        if not video_b_loader.is_loaded():
            raise ValueError("视频B未正确加载")
            
        self.video_a_loader = video_a_loader
        self.video_b_loader = video_b_loader
        
        self.logger.info("视频设置完成")
        self.logger.info(f"视频A: {video_a_loader.get_current_info()}")
        self.logger.info(f"视频B: {video_b_loader.get_current_info()}")
    
    def direct_insertion(self, positions: List[InsertionPosition], 
                        resize_mode: str = "fit") -> List[np.ndarray]:
        """直接插入A视频到B视频指定位置
        
        Args:
            positions: 插入位置列表
            resize_mode: 调整大小模式 ("fit", "stretch", "crop", "pad")
        
        Returns:
            融合后的帧序列
        """
        try:
            if not self.video_a_loader or not self.video_b_loader:
                raise ValueError("视频未设置")
            
            video_a_info = self.video_a_loader.get_current_info()
            video_b_info = self.video_b_loader.get_current_info()
            
            self.logger.info(f"开始直接插入，插入位置数量: {len(positions)}")
            
            # 获取B视频的所有帧
            b_frames = []
            for frame_number, frame in self.video_b_loader.get_frame_iterator():
                b_frames.append(frame.copy())
            
            # 获取A视频的帧
            a_frames = []
            for frame_number, frame in self.video_a_loader.get_frame_iterator():
                # 调整A视频帧大小以匹配B视频
                resized_frame = self._resize_frame(frame, 
                                                 (video_b_info.width, video_b_info.height), 
                                                 resize_mode)
                a_frames.append(resized_frame)
            
            if not a_frames:
                raise ValueError("A视频没有可用帧")
            
            # 执行插入
            result_frames = []
            a_frame_index = 0
            
            for b_frame_index, b_frame in enumerate(b_frames):
                # 检查当前位置是否需要插入
                should_insert = False
                insert_duration = 0
                
                for pos in positions:
                    if pos.frame_number == b_frame_index:
                        should_insert = True
                        insert_duration = pos.duration
                        break
                
                if should_insert:
                    # 插入A视频帧
                    for i in range(insert_duration):
                        if a_frame_index < len(a_frames):
                            result_frames.append(a_frames[a_frame_index].copy())
                            a_frame_index += 1
                        else:
                            # A视频帧用完，重复最后一帧
                            result_frames.append(a_frames[-1].copy())
                    
                    self.logger.debug(f"在位置 {b_frame_index} 插入了 {insert_duration} 帧")
                
                # 添加B视频帧
                result_frames.append(b_frame.copy())
            
            self.result_frames = result_frames
            self.logger.info(f"直接插入完成，结果帧数: {len(result_frames)}")
            return result_frames
            
        except Exception as e:
            self.logger.error(f"直接插入失败: {e}")
            raise
    
    def replace_insertion(self, positions: List[InsertionPosition], 
                         resize_mode: str = "fit") -> List[np.ndarray]:
        """替换插入A视频到B视频指定位置
        
        Args:
            positions: 插入位置列表
            resize_mode: 调整大小模式
        
        Returns:
            融合后的帧序列
        """
        try:
            if not self.video_a_loader or not self.video_b_loader:
                raise ValueError("视频未设置")
            
            video_a_info = self.video_a_loader.get_current_info()
            video_b_info = self.video_b_loader.get_current_info()
            
            self.logger.info(f"开始替换插入，插入位置数量: {len(positions)}")
            
            # 获取B视频的所有帧
            b_frames = []
            for frame_number, frame in self.video_b_loader.get_frame_iterator():
                b_frames.append(frame.copy())
            
            # 获取A视频的帧
            a_frames = []
            for frame_number, frame in self.video_a_loader.get_frame_iterator():
                resized_frame = self._resize_frame(frame, 
                                                 (video_b_info.width, video_b_info.height), 
                                                 resize_mode)
                a_frames.append(resized_frame)
            
            if not a_frames:
                raise ValueError("A视频没有可用帧")
            
            # 创建替换映射
            replacement_map = {}
            a_frame_index = 0
            
            for pos in positions:
                for i in range(pos.duration):
                    frame_pos = pos.frame_number + i
                    if frame_pos < len(b_frames) and a_frame_index < len(a_frames):
                        replacement_map[frame_pos] = a_frames[a_frame_index]
                        a_frame_index += 1
            
            # 执行替换
            result_frames = []
            for b_frame_index, b_frame in enumerate(b_frames):
                if b_frame_index in replacement_map:
                    result_frames.append(replacement_map[b_frame_index].copy())
                    self.logger.debug(f"替换位置 {b_frame_index} 的帧")
                else:
                    result_frames.append(b_frame.copy())
            
            self.result_frames = result_frames
            self.logger.info(f"替换插入完成，结果帧数: {len(result_frames)}")
            return result_frames
            
        except Exception as e:
            self.logger.error(f"替换插入失败: {e}")
            raise
    
    def segment_insertion(self, segment_positions: List[List[InsertionPosition]], 
                         resize_mode: str = "fit") -> List[np.ndarray]:
        """分段插入A视频到B视频多个位置
        
        Args:
            segment_positions: 分段插入位置列表，每个元素是一个位置列表
            resize_mode: 调整大小模式
        
        Returns:
            融合后的帧序列
        """
        try:
            if not self.video_a_loader or not self.video_b_loader:
                raise ValueError("视频未设置")
            
            self.logger.info(f"开始分段插入，段数: {len(segment_positions)}")
            
            # 获取A视频的帧
            a_frames = []
            video_b_info = self.video_b_loader.get_current_info()
            
            for frame_number, frame in self.video_a_loader.get_frame_iterator():
                resized_frame = self._resize_frame(frame, 
                                                 (video_b_info.width, video_b_info.height), 
                                                 resize_mode)
                a_frames.append(resized_frame)
            
            if not a_frames:
                raise ValueError("A视频没有可用帧")
            
            # 计算每段的帧数
            total_segments = len(segment_positions)
            frames_per_segment = len(a_frames) // total_segments
            
            # 为每段分配A视频帧
            segment_frames = []
            for i in range(total_segments):
                start_idx = i * frames_per_segment
                if i == total_segments - 1:  # 最后一段包含剩余所有帧
                    end_idx = len(a_frames)
                else:
                    end_idx = start_idx + frames_per_segment
                
                segment_frames.append(a_frames[start_idx:end_idx])
            
            # 获取B视频的所有帧
            b_frames = []
            for frame_number, frame in self.video_b_loader.get_frame_iterator():
                b_frames.append(frame.copy())
            
            # 执行分段插入
            result_frames = []
            
            for b_frame_index, b_frame in enumerate(b_frames):
                # 检查是否需要插入任何段
                for segment_idx, positions in enumerate(segment_positions):
                    for pos in positions:
                        if pos.frame_number == b_frame_index:
                            # 插入该段的帧
                            segment_frame_list = segment_frames[segment_idx]
                            insert_count = min(pos.duration, len(segment_frame_list))
                            
                            for i in range(insert_count):
                                result_frames.append(segment_frame_list[i].copy())
                            
                            self.logger.debug(f"在位置 {b_frame_index} 插入段 {segment_idx}，{insert_count} 帧")
                
                # 添加B视频帧
                result_frames.append(b_frame.copy())
            
            self.result_frames = result_frames
            self.logger.info(f"分段插入完成，结果帧数: {len(result_frames)}")
            return result_frames
            
        except Exception as e:
            self.logger.error(f"分段插入失败: {e}")
            raise
    
    def _resize_frame(self, frame: np.ndarray, target_size: Tuple[int, int], 
                     mode: str = "fit") -> np.ndarray:
        """调整帧大小
        
        Args:
            frame: 输入帧
            target_size: 目标尺寸 (width, height)
            mode: 调整模式 ("fit", "stretch", "crop", "pad")
        
        Returns:
            调整后的帧
        """
        try:
            target_w, target_h = target_size
            h, w = frame.shape[:2]
            
            if mode == "stretch":
                # 直接拉伸到目标尺寸
                return cv2.resize(frame, (target_w, target_h))
            
            elif mode == "fit":
                # 保持宽高比，适应目标尺寸
                scale = min(target_w / w, target_h / h)
                new_w = int(w * scale)
                new_h = int(h * scale)
                
                resized = cv2.resize(frame, (new_w, new_h))
                
                # 创建目标大小的黑色背景
                result = np.zeros((target_h, target_w, frame.shape[2]), dtype=frame.dtype)
                
                # 居中放置
                y_offset = (target_h - new_h) // 2
                x_offset = (target_w - new_w) // 2
                result[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = resized
                
                return result
            
            elif mode == "crop":
                # 保持宽高比，裁剪到目标尺寸
                scale = max(target_w / w, target_h / h)
                new_w = int(w * scale)
                new_h = int(h * scale)
                
                resized = cv2.resize(frame, (new_w, new_h))
                
                # 居中裁剪
                y_offset = (new_h - target_h) // 2
                x_offset = (new_w - target_w) // 2
                
                return resized[y_offset:y_offset+target_h, x_offset:x_offset+target_w]
            
            elif mode == "pad":
                # 保持原始尺寸，填充到目标尺寸
                result = np.zeros((target_h, target_w, frame.shape[2]), dtype=frame.dtype)
                
                # 计算放置位置
                y_offset = (target_h - h) // 2
                x_offset = (target_w - w) // 2
                
                # 确保不超出边界
                y_end = min(y_offset + h, target_h)
                x_end = min(x_offset + w, target_w)
                frame_h = y_end - y_offset
                frame_w = x_end - x_offset
                
                result[y_offset:y_end, x_offset:x_end] = frame[:frame_h, :frame_w]
                
                return result
            
            else:
                raise ValueError(f"不支持的调整模式: {mode}")
                
        except Exception as e:
            self.logger.error(f"调整帧大小失败: {e}")
            return frame

    def _resize_frame_with_spatial_control(self, frame: np.ndarray, target_size: Tuple[int, int],
                                         spatial_control) -> np.ndarray:
        """使用空间尺寸控制参数调整帧大小

        Args:
            frame: 输入帧
            target_size: 目标尺寸 (width, height)
            spatial_control: 空间尺寸控制参数

        Returns:
            调整后的帧
        """
        try:
            from .fusion_engine import ScaleMode

            target_w, target_h = target_size
            h, w = frame.shape[:2]

            # 如果不对齐主视频，直接返回原帧
            if not spatial_control.align_to_main_video:
                return frame

            # 计算缩放后的目标尺寸
            scaled_target_w = int(target_w * spatial_control.scale_ratio)
            scaled_target_h = int(target_h * spatial_control.scale_ratio)

            # 根据缩放模式处理
            if spatial_control.scale_mode == ScaleMode.PROPORTIONAL:
                # 等比例缩放（保持长宽比）
                if spatial_control.maintain_aspect_ratio:
                    scale = min(scaled_target_w / w, scaled_target_h / h)
                    new_w = int(w * scale)
                    new_h = int(h * scale)
                else:
                    new_w = scaled_target_w
                    new_h = scaled_target_h

                resized = cv2.resize(frame, (new_w, new_h))

                # 创建目标大小的背景
                result = np.zeros((target_h, target_w, frame.shape[2]), dtype=frame.dtype)

                # 居中放置
                y_offset = (target_h - new_h) // 2
                x_offset = (target_w - new_w) // 2

                # 确保不超出边界
                y_end = min(y_offset + new_h, target_h)
                x_end = min(x_offset + new_w, target_w)
                actual_h = y_end - y_offset
                actual_w = x_end - x_offset

                result[y_offset:y_end, x_offset:x_end] = resized[:actual_h, :actual_w]
                return result

            elif spatial_control.scale_mode == ScaleMode.STRETCH:
                # 拉伸缩放（不保持长宽比）
                return cv2.resize(frame, (scaled_target_w, scaled_target_h))

            elif spatial_control.scale_mode == ScaleMode.CROP:
                # 裁剪缩放（保持长宽比，裁剪多余部分）
                if spatial_control.maintain_aspect_ratio:
                    scale = max(scaled_target_w / w, scaled_target_h / h)
                    new_w = int(w * scale)
                    new_h = int(h * scale)

                    resized = cv2.resize(frame, (new_w, new_h))

                    # 居中裁剪
                    y_offset = (new_h - scaled_target_h) // 2
                    x_offset = (new_w - scaled_target_w) // 2

                    cropped = resized[y_offset:y_offset+scaled_target_h,
                                    x_offset:x_offset+scaled_target_w]

                    # 如果缩放比例不是1.0，需要放置到目标尺寸的背景中
                    if spatial_control.scale_ratio != 1.0:
                        result = np.zeros((target_h, target_w, frame.shape[2]), dtype=frame.dtype)
                        y_pos = (target_h - scaled_target_h) // 2
                        x_pos = (target_w - scaled_target_w) // 2
                        result[y_pos:y_pos+scaled_target_h, x_pos:x_pos+scaled_target_w] = cropped
                        return result
                    else:
                        return cropped
                else:
                    return cv2.resize(frame, (scaled_target_w, scaled_target_h))

            elif spatial_control.scale_mode == ScaleMode.PAD:
                # 填充缩放（保持原始比例，填充空白区域）
                if spatial_control.maintain_aspect_ratio:
                    scale = min(scaled_target_w / w, scaled_target_h / h)
                    new_w = int(w * scale)
                    new_h = int(h * scale)
                else:
                    new_w = min(w, scaled_target_w)
                    new_h = min(h, scaled_target_h)

                resized = cv2.resize(frame, (new_w, new_h))

                # 创建目标大小的背景
                result = np.zeros((target_h, target_w, frame.shape[2]), dtype=frame.dtype)

                # 居中放置
                y_offset = (target_h - new_h) // 2
                x_offset = (target_w - new_w) // 2
                result[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = resized

                return result

            else:
                # 默认使用等比例缩放
                return self._resize_frame_with_spatial_control(frame, target_size,
                    type('SpatialSizeControl', (), {
                        'align_to_main_video': True,
                        'scale_ratio': spatial_control.scale_ratio,
                        'maintain_aspect_ratio': True,
                        'scale_mode': ScaleMode.PROPORTIONAL
                    })())

        except Exception as e:
            self.logger.error(f"使用空间尺寸控制调整帧大小失败: {e}")
            # 回退到原始方法
            return self._resize_frame(frame, target_size, "fit")
    
    def get_result_frames(self) -> List[np.ndarray]:
        """获取融合结果帧"""
        return self.result_frames.copy()
    
    def save_result_video(self, output_path: str, fps: float = 30.0, 
                         codec: str = 'mp4v') -> bool:
        """保存融合结果为视频文件
        
        Args:
            output_path: 输出文件路径
            fps: 帧率
            codec: 编码器
        
        Returns:
            是否保存成功
        """
        try:
            if not self.result_frames:
                self.logger.error("没有可保存的结果帧")
                return False
            
            # 获取第一帧的尺寸
            first_frame = self.result_frames[0]
            h, w = first_frame.shape[:2]
            
            # 创建视频写入器
            fourcc = cv2.VideoWriter_fourcc(*codec)
            video_writer = cv2.VideoWriter(output_path, fourcc, fps, (w, h))
            
            if not video_writer.isOpened():
                self.logger.error(f"无法创建视频写入器: {output_path}")
                return False
            
            # 写入所有帧
            for i, frame in enumerate(self.result_frames):
                video_writer.write(frame)
                
                if (i + 1) % 100 == 0:
                    self.logger.info(f"已写入 {i + 1}/{len(self.result_frames)} 帧")
            
            video_writer.release()
            self.logger.info(f"视频保存完成: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存视频失败: {e}")
            return False
    
    def get_insertion_preview(self, positions: List[InsertionPosition], 
                            max_frames: int = 10) -> List[Tuple[int, np.ndarray]]:
        """获取插入预览
        
        Args:
            positions: 插入位置列表
            max_frames: 最大预览帧数
        
        Returns:
            预览帧列表 (帧号, 帧图像)
        """
        try:
            if not self.video_a_loader or not self.video_b_loader:
                raise ValueError("视频未设置")
            
            preview_frames = []
            video_b_info = self.video_b_loader.get_current_info()
            
            # 获取A视频的第一帧作为预览
            a_frame = self.video_a_loader.get_frame(0)
            if a_frame is None:
                return preview_frames
            
            # 调整A帧大小
            a_frame_resized = self._resize_frame(a_frame, 
                                               (video_b_info.width, video_b_info.height), 
                                               "fit")
            
            # 为每个插入位置生成预览
            for i, pos in enumerate(positions[:max_frames]):
                # 获取B视频在插入位置的帧
                b_frame = self.video_b_loader.get_frame(pos.frame_number)
                if b_frame is not None:
                    # 创建预览：左边是B帧，右边是A帧
                    preview = self._create_side_by_side_preview(b_frame, a_frame_resized)
                    preview_frames.append((pos.frame_number, preview))
            
            self.logger.info(f"生成插入预览，预览帧数: {len(preview_frames)}")
            return preview_frames
            
        except Exception as e:
            self.logger.error(f"生成插入预览失败: {e}")
            return []
    
    def _create_side_by_side_preview(self, frame_b: np.ndarray, 
                                   frame_a: np.ndarray) -> np.ndarray:
        """创建并排预览图像"""
        try:
            h, w = frame_b.shape[:2]
            
            # 调整A帧大小以匹配B帧
            frame_a_resized = cv2.resize(frame_a, (w, h))
            
            # 创建并排图像
            preview = np.hstack([frame_b, frame_a_resized])
            
            # 添加分割线
            cv2.line(preview, (w, 0), (w, h), (255, 255, 255), 2)
            
            # 添加标签
            cv2.putText(preview, "B Video", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            cv2.putText(preview, "A Video", (w + 10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            
            return preview
            
        except Exception as e:
            self.logger.error(f"创建并排预览失败: {e}")
            return frame_b

    def generate_time_controlled_positions(self, time_control, video_b_frame_count: int) -> List[InsertionPosition]:
        """根据时间维度控制参数生成插入位置

        Args:
            time_control: 时间维度控制参数
            video_b_frame_count: B视频总帧数

        Returns:
            生成的插入位置列表
        """
        try:
            from .fusion_engine import TimeDistributionMode

            positions = []
            insertion_count = time_control.insertion_count
            distribution_mode = time_control.distribution_mode

            self.logger.info(f"生成时间控制插入位置: 插入次数={insertion_count}, 分布模式={distribution_mode.value}")

            if distribution_mode == TimeDistributionMode.UNIFORM:
                # 均匀分布
                positions = self._generate_uniform_positions(insertion_count, video_b_frame_count)

            elif distribution_mode == TimeDistributionMode.RANDOM:
                # 随机分布
                positions = self._generate_random_positions(insertion_count, video_b_frame_count)

            elif distribution_mode == TimeDistributionMode.FRONT_BIASED:
                # 偏向前段
                positions = self._generate_biased_positions(insertion_count, video_b_frame_count,
                                                          "front", time_control.time_bias_strength)

            elif distribution_mode == TimeDistributionMode.MIDDLE_BIASED:
                # 偏向中段
                positions = self._generate_biased_positions(insertion_count, video_b_frame_count,
                                                          "middle", time_control.time_bias_strength)

            elif distribution_mode == TimeDistributionMode.REAR_BIASED:
                # 偏向后段
                positions = self._generate_biased_positions(insertion_count, video_b_frame_count,
                                                          "rear", time_control.time_bias_strength)

            elif distribution_mode == TimeDistributionMode.CUSTOM:
                # 自定义时间点
                positions = self._generate_custom_positions(time_control.custom_time_points,
                                                          video_b_frame_count)

            self.logger.info(f"生成了 {len(positions)} 个插入位置")
            return positions

        except Exception as e:
            self.logger.error(f"生成时间控制插入位置失败: {e}")
            return []

    def _generate_uniform_positions(self, count: int, total_frames: int) -> List[InsertionPosition]:
        """生成均匀分布的插入位置"""
        positions = []
        if count <= 0 or total_frames <= 0:
            return positions

        # 限制插入次数不超过总帧数
        actual_count = min(count, total_frames)

        # 计算均匀间隔
        interval = total_frames / (actual_count + 1)

        for i in range(actual_count):
            frame_number = int((i + 1) * interval)
            # 确保不超出范围
            frame_number = min(frame_number, total_frames - 1)
            positions.append(InsertionPosition(frame_number, 1))

        self.logger.debug(f"均匀分布生成位置: {[pos.frame_number for pos in positions]}")
        return positions

    def _generate_random_positions(self, count: int, total_frames: int) -> List[InsertionPosition]:
        """生成随机分布的插入位置"""
        positions = []
        if count <= 0 or total_frames <= 0:
            return positions

        # 限制插入次数不超过总帧数
        actual_count = min(count, total_frames)

        # 生成随机位置，避免重复
        frame_numbers = random.sample(range(total_frames), actual_count)
        frame_numbers.sort()  # 按时间顺序排序

        for frame_number in frame_numbers:
            positions.append(InsertionPosition(frame_number, 1))

        self.logger.debug(f"随机分布生成位置: {[pos.frame_number for pos in positions]}")
        return positions

    def _generate_biased_positions(self, count: int, total_frames: int,
                                 bias_type: str, bias_strength: float) -> List[InsertionPosition]:
        """生成偏向分布的插入位置

        Args:
            count: 插入次数
            total_frames: 总帧数
            bias_type: 偏向类型 ("front", "middle", "rear")
            bias_strength: 偏向强度 (0.5-1.0)
        """
        positions = []
        if count <= 0 or total_frames <= 0:
            return positions

        # 限制插入次数不超过总帧数
        actual_count = min(count, total_frames)

        # 限制偏向强度范围
        bias_strength = max(0.5, min(1.0, bias_strength))

        if bias_type == "front":
            # 偏向前段：前1/3部分权重更高
            bias_range = (0, total_frames // 3)
        elif bias_type == "middle":
            # 偏向中段：中1/3部分权重更高
            bias_range = (total_frames // 3, 2 * total_frames // 3)
        elif bias_type == "rear":
            # 偏向后段：后1/3部分权重更高
            bias_range = (2 * total_frames // 3, total_frames)
        else:
            # 默认为均匀分布
            return self._generate_uniform_positions(actual_count, total_frames)

        # 计算偏向区域和非偏向区域的插入数量
        biased_count = int(actual_count * bias_strength)
        normal_count = actual_count - biased_count

        # 在偏向区域生成位置
        if bias_range[1] > bias_range[0]:
            biased_positions = random.sample(range(bias_range[0], bias_range[1]),
                                           min(biased_count, bias_range[1] - bias_range[0]))
        else:
            biased_positions = []

        # 在非偏向区域生成位置
        normal_range = list(range(total_frames))
        for pos in range(bias_range[0], bias_range[1]):
            if pos in normal_range:
                normal_range.remove(pos)

        if normal_count > 0 and normal_range:
            normal_positions = random.sample(normal_range, min(normal_count, len(normal_range)))
        else:
            normal_positions = []

        # 合并并排序
        all_positions = biased_positions + normal_positions
        all_positions.sort()

        for frame_number in all_positions:
            positions.append(InsertionPosition(frame_number, 1))

        self.logger.debug(f"{bias_type}偏向分布生成位置: {[pos.frame_number for pos in positions]}")
        return positions

    def _generate_custom_positions(self, time_points: List[float],
                                 total_frames: int) -> List[InsertionPosition]:
        """根据自定义时间点生成插入位置

        Args:
            time_points: 时间点列表（百分比，0.0-1.0）
            total_frames: 总帧数
        """
        positions = []
        if not time_points or total_frames <= 0:
            return positions

        for time_point in time_points:
            # 确保时间点在有效范围内
            time_point = max(0.0, min(1.0, time_point))
            frame_number = int(time_point * total_frames)
            # 确保不超出范围
            frame_number = min(frame_number, total_frames - 1)
            positions.append(InsertionPosition(frame_number, 1))

        # 按时间顺序排序
        positions.sort(key=lambda pos: pos.frame_number)

        self.logger.debug(f"自定义时间点生成位置: {[pos.frame_number for pos in positions]}")
        return positions

    def time_controlled_insertion(self, time_control, insertion_mode: str = "direct",
                                resize_mode: str = "fit") -> List[np.ndarray]:
        """使用时间维度控制执行插入融合

        Args:
            time_control: 时间维度控制参数
            insertion_mode: 插入模式 ("direct", "replace")
            resize_mode: 调整大小模式

        Returns:
            融合后的帧序列
        """
        try:
            if not self.video_a_loader or not self.video_b_loader:
                raise ValueError("视频未设置")

            video_b_info = self.video_b_loader.get_current_info()
            video_b_frame_count = video_b_info.frame_count

            # 根据时间控制参数生成插入位置
            positions = self.generate_time_controlled_positions(time_control, video_b_frame_count)

            if not positions:
                self.logger.warning("没有生成任何插入位置，返回原始B视频")
                # 返回原始B视频帧
                result_frames = []
                for frame_number, frame in self.video_b_loader.get_frame_iterator():
                    result_frames.append(frame.copy())
                return result_frames

            # 根据插入模式执行融合
            if insertion_mode == "direct":
                return self.direct_insertion(positions, resize_mode)
            elif insertion_mode == "replace":
                return self.replace_insertion(positions, resize_mode)
            else:
                raise ValueError(f"不支持的插入模式: {insertion_mode}")

        except Exception as e:
            self.logger.error(f"时间控制插入融合失败: {e}")
            raise

    def get_time_controlled_preview(self, time_control, max_frames: int = 10) -> List[Tuple[int, np.ndarray]]:
        """获取时间控制插入的预览

        Args:
            time_control: 时间维度控制参数
            max_frames: 最大预览帧数

        Returns:
            预览帧列表 (帧号, 帧图像)
        """
        try:
            if not self.video_a_loader or not self.video_b_loader:
                raise ValueError("视频未设置")

            video_b_info = self.video_b_loader.get_current_info()
            video_b_frame_count = video_b_info.frame_count

            # 根据时间控制参数生成插入位置
            positions = self.generate_time_controlled_positions(time_control, video_b_frame_count)

            # 限制预览帧数
            preview_positions = positions[:max_frames]

            # 生成预览
            return self.get_insertion_preview(preview_positions, max_frames)

        except Exception as e:
            self.logger.error(f"获取时间控制预览失败: {e}")
            return []

    def spatial_controlled_insertion(self, positions: List[InsertionPosition],
                                   spatial_control, insertion_mode: str = "direct") -> List[np.ndarray]:
        """使用空间尺寸控制执行插入融合

        Args:
            positions: 插入位置列表
            spatial_control: 空间尺寸控制参数
            insertion_mode: 插入模式 ("direct", "replace")

        Returns:
            融合后的帧序列
        """
        try:
            if not self.video_a_loader or not self.video_b_loader:
                raise ValueError("视频未设置")

            video_a_info = self.video_a_loader.get_current_info()
            video_b_info = self.video_b_loader.get_current_info()

            self.logger.info(f"开始空间控制插入，插入位置数量: {len(positions)}")
            self.logger.info(f"空间控制参数: 对齐={spatial_control.align_to_main_video}, "
                           f"缩放比例={spatial_control.scale_ratio}, "
                           f"保持长宽比={spatial_control.maintain_aspect_ratio}, "
                           f"缩放模式={spatial_control.scale_mode.value}")

            # 获取B视频的所有帧
            b_frames = []
            for frame_number, frame in self.video_b_loader.get_frame_iterator():
                b_frames.append(frame.copy())

            # 获取A视频的帧并应用空间尺寸控制
            a_frames = []
            for frame_number, frame in self.video_a_loader.get_frame_iterator():
                # 使用空间尺寸控制调整A视频帧
                resized_frame = self._resize_frame_with_spatial_control(
                    frame, (video_b_info.width, video_b_info.height), spatial_control)
                a_frames.append(resized_frame)

            if not a_frames:
                raise ValueError("A视频没有可用帧")

            # 根据插入模式执行融合
            if insertion_mode == "direct":
                return self._execute_direct_insertion_with_frames(positions, a_frames, b_frames)
            elif insertion_mode == "replace":
                return self._execute_replace_insertion_with_frames(positions, a_frames, b_frames)
            else:
                raise ValueError(f"不支持的插入模式: {insertion_mode}")

        except Exception as e:
            self.logger.error(f"空间控制插入融合失败: {e}")
            raise

    def _execute_direct_insertion_with_frames(self, positions: List[InsertionPosition],
                                            a_frames: List[np.ndarray],
                                            b_frames: List[np.ndarray]) -> List[np.ndarray]:
        """使用预处理的帧执行直接插入"""
        result_frames = []
        a_frame_index = 0

        for b_frame_index, b_frame in enumerate(b_frames):
            # 检查当前位置是否需要插入
            should_insert = False
            insert_duration = 0

            for pos in positions:
                if pos.frame_number == b_frame_index:
                    should_insert = True
                    insert_duration = pos.duration
                    break

            if should_insert:
                # 插入A视频帧
                for i in range(insert_duration):
                    if a_frame_index < len(a_frames):
                        result_frames.append(a_frames[a_frame_index].copy())
                        a_frame_index += 1
                    else:
                        # A视频帧用完，重复最后一帧
                        result_frames.append(a_frames[-1].copy())

                self.logger.debug(f"在位置 {b_frame_index} 插入了 {insert_duration} 帧")

            # 添加B视频帧
            result_frames.append(b_frame.copy())

        self.result_frames = result_frames
        self.logger.info(f"空间控制直接插入完成，结果帧数: {len(result_frames)}")
        return result_frames

    def _execute_replace_insertion_with_frames(self, positions: List[InsertionPosition],
                                             a_frames: List[np.ndarray],
                                             b_frames: List[np.ndarray]) -> List[np.ndarray]:
        """使用预处理的帧执行替换插入"""
        # 创建替换映射
        replacement_map = {}
        a_frame_index = 0

        for pos in positions:
            for i in range(pos.duration):
                frame_pos = pos.frame_number + i
                if frame_pos < len(b_frames) and a_frame_index < len(a_frames):
                    replacement_map[frame_pos] = a_frames[a_frame_index]
                    a_frame_index += 1

        # 执行替换
        result_frames = []
        for b_frame_index, b_frame in enumerate(b_frames):
            if b_frame_index in replacement_map:
                result_frames.append(replacement_map[b_frame_index].copy())
                self.logger.debug(f"替换位置 {b_frame_index} 的帧")
            else:
                result_frames.append(b_frame.copy())

        self.result_frames = result_frames
        self.logger.info(f"空间控制替换插入完成，结果帧数: {len(result_frames)}")
        return result_frames
